name: Build solutions

on:
  pull_request:
    branches: [master]
  push:
    branches: [master]
  merge_group:

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        config: [release, debug]
        solution: [Nethermind, EthereumTests, Benchmarks]
    steps:
      - name: Free up disk space
        uses: jlumbroso/free-disk-space@main
        with:
          large-packages: false
          tool-cache: false
      - name: Check out repository
        uses: actions/checkout@v5
        with:
          submodules: ${{ matrix.solution == 'EthereumTests' }}
      - name: Set up .NET
        uses: actions/setup-dotnet@v5
      - name: Build ${{ matrix.solution }}.slnx
        run: dotnet build src/Nethermind/${{ matrix.solution }}.slnx -c ${{ matrix.config }}
