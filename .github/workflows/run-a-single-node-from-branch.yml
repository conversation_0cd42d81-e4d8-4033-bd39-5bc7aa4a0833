name: "Run a node with selected configuration"

on:
  workflow_dispatch:
    inputs:
      network:
        description: "Select a network on which You want to run a node"
        default: "mainnet"
        required: true
        type: choice
        options:
          - mainnet
          - gnosis
          - sepolia
          - chiado
          - holesky
          - hoodi
          - op-mainnet
          - op-sepolia
          - base-mainnet
          - base-sepolia          
          - world-sepolia
          - world-mainnet
          - joc-mainnet
          - joc-testnet
          - linea-mainnet
          - linea-sepolia
          - taiko-alethia
          - taiko-hekla
          - energyweb
          - volta
      cl_client:
        description: "Select Consensus Layer Client to run node against (for Volta or Energyweb just set any - it will be omitted)"
        default: ""
        required: true
        type: choice
        options:
          - lighthouse
          - lodestar
          - prysm
          - teku
          - nimbus
          - nimbus_ws
      cl_custom_image:
        description: "In case of need to run non-default cl image (different than actually supported by Sedge) put it in there"
        default: ""
        required: false
      config:
        description: "Select a config file which will be selected for tests."
        default: "default.json"
        required: true
        type: choice
        options:
          - default.json
          - archiveSync.json
          - fastSync.json
          - fullSync.json
          - fuzzer.json
      non_validator_mode:
        description: "If checked, node will be started in NonValidator mode (OldBodies and OldReceipts will not be synced)"
        default: true
        type: boolean
      additional_nethermind_flags:
        description: 'Provide any additional flags to the Nethermind L1 node in space-separated format. Example: "JsonRpc.Enabled=false Sync.SnapSync=false".'
        default: ""
        required: false
      additional_cl_flags:
        description: 'Provide any additional flags to the CL client in space-separated format. Example: "clflag1=1 clflag2=2".'
        default: ""
        required: false
      additional_options:
        description: "A Json property which allows to customize node even more"
        default: '{"timeout":"24", "default_dockerfile":"Dockerfile", "default_dockerfile_build_type":"release", "ssh_keys":"", "allowed_ips":"", "custom_machine_type": ""}'
      additional_l2_options:
        description: "Extra options to configure L2 node"
        default: '{"l1_nethermind_image":"", "l2_node_image":"", "l2_el_extra_flags":"", "l2_cl_extra_flags":"", "layer1_el_endpoint":"", "layer1_cl_endpoint":""}'
      convert_to_paprika:
        description: "If checked, will attempt to convert node to paprika schema using 'paprika' branch."
        default: false
        type: boolean
  workflow_call:
    inputs:
      smoke_tests_ref:
        description: "Ref of the smoke tests repository to be used for smoke tests"
        default: "main"
        required: false
        type: string
      nethermind_repo_ref:
        description: "Ref of the nethermind repo in case it is needed to be changed"
        default: ""
        required: false
        type: string
      custom_run_id:
        description: "Run ID used for artifacts save"
        default: ""
        required: false
        type: string
      network:
        description: "Select a network on which You want to run a node"
        default: "mainnet"
        required: false
        type: string
      cl_client:
        description: "Select Consensus Layer Client to run node against"
        default: "lighthouse"
        required: false
        type: string
      cl_custom_image:
        description: "In case of need to run non-default cl image (different than actually supported by Sedge) put it in there"
        default: ""
        required: false
        type: string
      config:
        description: "Select a config file which will be selected for tests."
        default: "default.json"
        required: false
        type: string
      non_validator_mode:
        description: "If checked, node will be started in NonValidator mode (OldBodies and oldReceipts will not be synced)"
        default: true
        type: boolean
      additional_nethermind_flags:
        type: string
        description: 'Provide any additional flags to the Nethermind in space-separated format. Example: "JsonRpc.Enabled=false Sync.SnapSync=false".'
        default: ""
        required: false
      additional_cl_flags:
        type: string
        description: 'Provide any additional flags to the CL client in space-separated format. Example: "clflag1=1 clflag2=2".'
        default: ""
        required: false
      additional_options:
        type: string
        description: "A Json property which allows to customize node even more"
        default: '{"timeout":"24", "default_dockerfile":"Dockerfile", "default_dockerfile_build_type":"release", "ssh_keys":"", "allowed_ips":"", "custom_machine_type": ""}'
        required: false
      additional_l2_options:
        type: string
        description: "Extra options to configure L2 node"
        default: '{"l1_nethermind_image":"", "l2_node_image":"", "l2_el_extra_flags":"", "l2_cl_extra_flags":"", "layer1_el_endpoint":"", "layer1_cl_endpoint":""}'
        required: false
      convert_to_paprika:
        description: "If checked, will attempt to convert node to paprika schema using 'paprika' branch."
        default: false
        type: boolean
    outputs:
      base_tag:
        description: ""
        value: ${{ jobs.create_docker_image.outputs.base_tag }}
      run_id:
        description: ""
        value: ${{ jobs.trigger_node_and_vm_creation.outputs.run_id }}
      rpc_url:
        description: ""
        value: ${{ jobs.trigger_node_and_vm_creation.outputs.rpc_url }}

jobs:
  create_docker_image:
    runs-on: ubuntu-latest
    outputs:
      base_tag: ${{ steps.set-base-tag.outputs.base_tag }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          ref: ${{ github.ref }}

      - name: Authenticate App
        id: gh-app
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}

      - name: Prepare docker tag
        id: prepare_ref
        run: |
          REF_NAME=${{ inputs.nethermind_repo_ref || github.ref }}
          CLEAN_REF=$(echo "${REF_NAME/refs\/heads\//}" | sed 's/[^a-zA-Z0-9._-]/-/g')
          echo "CLEAN_REF=$CLEAN_REF" >> $GITHUB_ENV

      - name: Set BASE_TAG
        id: set-base-tag
        env:
          GITHUB_USERNAME: ${{ github.actor }}
        run: |
          BASE_TAG="${GITHUB_USERNAME:0:1}$(shuf -i 1000-9999 -n 1)"
          echo "BASE_TAG=$BASE_TAG" >> $GITHUB_ENV
          echo "base_tag=$BASE_TAG" >> $GITHUB_OUTPUT

      - name: Creating a node with NodeName="DevNode-${{ github.actor }}-${{ env.BASE_TAG }}-${{ env.CLEAN_REF }}-${{ inputs.network }}-${{ inputs.cl_client }}"
        run: echo "NodeName='DevNode-${{ github.actor }}-${{ env.BASE_TAG }}-${{ env.CLEAN_REF }}-${{ inputs.network }}-${{ inputs.cl_client }}'"

      - name: Extract dockerfile from additional_options
        id: extract_dockerfile
        run: |
          echo "dockerfile=$(echo '${{ inputs.additional_options }}' | jq -r .default_dockerfile)" >> $GITHUB_OUTPUT
          echo "build-config=$(echo '${{ inputs.additional_options }}' | jq -r .default_dockerfile_build_type | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT

      - name: Set Repo and Org Variables
        run: |
          echo "ORG_NAME=${{ github.repository_owner }}" >> $GITHUB_ENV
          echo "REPO_NAME=${{ github.event.repository.name }}" >> $GITHUB_ENV

      - name: Check if master branch and default additional_options
        id: check_conditions
        run: |
          ref="${{ inputs.nethermind_repo_ref || github.ref }}"
          if [ -z "$ref" ]; then
            ref="${{ github.ref }}"
          fi
          # Append "refs/heads/" prefix if it's not already there
          if [[ $ref != refs/heads/* ]]; then
            ref="refs/heads/$ref"
          fi

          if
          [[ "$ref" == "refs/heads/master" || $ref == refs/heads/release* || "$ref" == "refs/heads/paprika" ]] &&
          [[ "${{ steps.extract_dockerfile.outputs.dockerfile }}" == "Dockerfile" ]] &&
          [[ "${{ steps.extract_dockerfile.outputs.build-config }}" == "release" ]]; then
            echo "skip_docker_build=true" >> $GITHUB_OUTPUT
          else
            echo "skip_docker_build=false" >> $GITHUB_OUTPUT
          fi

      - name: Trigger Docker Build Action with Cleaned Ref
        if: steps.check_conditions.outputs.skip_docker_build != 'true'
        uses: benc-uk/workflow-dispatch@v1
        env:
          ADDITIONAL_OPTIONS: ${{ inputs.additional_options }}
        with:
          workflow: publish-docker.yml
          ref: "${{ inputs.nethermind_repo_ref || github.ref }}"
          token: "${{ steps.gh-app.outputs.token }}"
          inputs: '{
            "tag": "${{ env.CLEAN_REF }}",
            "dockerfile": "${{ steps.extract_dockerfile.outputs.dockerfile }}",
            "build-config": "${{ steps.extract_dockerfile.outputs.build-config }}"
            }'

      - name: Wait for Docker Build Action to complete
        if: steps.check_conditions.outputs.skip_docker_build != 'true'
        env:
          GITHUB_TOKEN: ${{ steps.gh-app.outputs.token }}
          WORKFLOW_ID: "publish-docker.yml"
          MAX_WAIT_MINUTES: "5"
          INTERVAL: "5"
          TIMEOUT: '20'
          ORG_NAME: ${{ env.ORG_NAME }}
          REPO_NAME: ${{ env.REPO_NAME }}
          REF: ${{ inputs.nethermind_repo_ref || github.ref }}
        run: |
          chmod +x scripts/wait-for-workflow.sh
          ./scripts/wait-for-workflow.sh
        working-directory: ${{ github.workspace }}

  trigger_node_and_vm_creation:
    needs: create_docker_image
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          ref: ${{ github.ref }}

      - name: Authenticate App
        id: gh-app
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
          repositories: "nethermind,post-merge-smoke-tests"

      - name: Prepare docker tag
        id: prepare_ref
        run: |
          REF_NAME=${{ inputs.nethermind_repo_ref || github.ref }}
          CLEAN_REF=$(echo "${REF_NAME/refs\/heads\//}" | sed 's/[^a-zA-Z0-9._-]/-/g')

          echo "CLEAN_REF=$CLEAN_REF" >> $GITHUB_ENV

      - name: Extract Variables
        id: extract_variables
        run: |
          echo "BASE_TAG=${{ needs.create_docker_image.outputs.base_tag }}" >> $GITHUB_ENV
          echo "timeout=$(echo '${{ inputs.additional_options }}' | jq -r .timeout)" >> $GITHUB_OUTPUT
          echo "ssh_keys=$(echo '${{ inputs.additional_options }}' | jq -r .ssh_keys)" >> $GITHUB_OUTPUT
          echo "allowed_ips=$(echo '${{ inputs.additional_options }}' | jq -r .allowed_ips)" >> $GITHUB_OUTPUT
          echo "custom_machine_type=$(echo '${{ inputs.additional_options }}' | jq -r .custom_machine_type)" >> $GITHUB_OUTPUT
          echo "l1_nethermind_image=$(echo '${{ inputs.additional_l2_options }}' | jq -r .l1_nethermind_image)" >> $GITHUB_OUTPUT
          echo "l2_node_image=$(echo '${{ inputs.additional_l2_options }}' | jq -r .l2_node_image)" >> $GITHUB_OUTPUT
          echo "l2_el_extra_flags=$(echo '${{ inputs.additional_l2_options }}' | jq -r .l2_el_extra_flags)" >> $GITHUB_OUTPUT
          echo "l2_cl_extra_flags=$(echo '${{ inputs.additional_l2_options }}' | jq -r .l2_cl_extra_flags)" >> $GITHUB_OUTPUT
          echo "layer1_el_endpoint=$(echo '${{ inputs.additional_l2_options }}' | jq -r .layer1_el_endpoint)" >> $GITHUB_OUTPUT
          echo "layer1_cl_endpoint=$(echo '${{ inputs.additional_l2_options }}' | jq -r .layer1_cl_endpoint)" >> $GITHUB_OUTPUT
          echo "NON_VALIDATOR_MODE=${{ inputs.non_validator_mode }}" >> $GITHUB_OUTPUT

      - name: Trigger Node creation Repo Action
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: run-single-node.yml
          repo: NethermindEth/post-merge-smoke-tests
          ref: "main"
          token: "${{ steps.gh-app.outputs.token }}"
          inputs: '{
            "github_username": "${{ github.actor }}",
            "base_tag": "${{ env.BASE_TAG }}",
            "config_file": "${{ inputs.config }}",
            "nethermind_branch": "${{ env.CLEAN_REF }}",
            "network": "${{ inputs.network }}",
            "cl_client": "${{ inputs.cl_client }}",
            "additional_options": "{\"cl_custom_image\":\"${{ inputs.cl_custom_image }}\", \"timeout\":\"${{ steps.extract_variables.outputs.timeout }}\", \"non_validator_mode\":${{ steps.extract_variables.outputs.NON_VALIDATOR_MODE }}, \"additional_nethermind_flags\":\"${{ inputs.additional_nethermind_flags }}\", \"additional_cl_flags\":\"${{ inputs.additional_cl_flags }}\", \"ssh_keys\":\"${{ steps.extract_variables.outputs.ssh_keys }}\", \"allowed_ips\":\"${{ steps.extract_variables.outputs.allowed_ips }}\", \"custom_machine_type\":\"${{ steps.extract_variables.outputs.custom_machine_type }}\", \"convert_to_paprika\": \"${{ inputs.convert_to_paprika }}\"}",
            "additional_l2_options": "{\"l1_nethermind_image\":\"${{ steps.extract_variables.outputs.l1_nethermind_image}}\", \"l2_node_image\":\"${{ steps.extract_variables.outputs.l2_node_image}}\", \"l2_el_extra_flags\":\"${{ steps.extract_variables.outputs.l2_el_extra_flags}}\", \"l2_cl_extra_flags\":\"${{ steps.extract_variables.outputs.l2_cl_extra_flags}}\", \"layer1_el_endpoint\":\"${{ steps.extract_variables.outputs.layer1_el_endpoint}}\", \"layer1_cl_endpoint\":\"${{ steps.extract_variables.outputs.layer1_cl_endpoint}}\"}"
            }'

      - name: Wait for creation of node
        env:
          GITHUB_TOKEN: ${{ steps.gh-app.outputs.token }}
          WORKFLOW_ID: "run-single-node.yml"
          MAX_WAIT_MINUTES: "5"
          INTERVAL: "5"
          TIMEOUT: "20"
          ORG_NAME: "NethermindEth"
          REPO_NAME: "post-merge-smoke-tests"
          NAME_FILTER: ${{ env.BASE_TAG }}
          REF: "main"
        run: |
          chmod +x scripts/wait-for-workflow.sh
          ./scripts/wait-for-workflow.sh | tee script-output.txt
          run_id=$(grep -oP 'Run ID: \K\d+' script-output.txt)
          echo "Run ID extracted is: $run_id"
          echo "RUN_ID=$run_id" >> $GITHUB_ENV
        working-directory: ${{ github.workspace }}

      - name: Download machine specs artifact
        run: |
          ARTIFACT_ID=$(curl -H "Accept: application/vnd.github.v3+json" -H "Authorization: token ${{ steps.gh-app.outputs.token }}" https://api.github.com/repos/NethermindEth/post-merge-smoke-tests/actions/runs/${{ env.RUN_ID }}/artifacts | jq '.artifacts[0].id')
          curl -L -H "Accept: application/vnd.github.v3+json" -H "Authorization: token ${{ steps.gh-app.outputs.token }}" -o artifact.zip https://api.github.com/repos/NethermindEth/post-merge-smoke-tests/actions/artifacts/$ARTIFACT_ID/zip
          unzip artifact.zip -d ./downloaded-artifacts/

      - name: Display machine specs content
        run: |
          FILE=$(ls downloaded-artifacts/machine-details | head -n 1)
          echo "<details>" >> $GITHUB_STEP_SUMMARY
          echo "<summary>Details of node from branch: ${{ env.CLEAN_REF }}</summary>" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          cat "downloaded-artifacts/machine-details/$FILE" >> $GITHUB_STEP_SUMMARY
          echo "</details>" >> $GITHUB_STEP_SUMMARY

          cat "downloaded-artifacts/machine-details/$FILE" | tee spec-output.txt
          rpc_url=$(grep -oP '\s{2,3}RPC URL: \K.+' spec-output.txt)
          echo "RPC URL extracted is: $rpc_url"
          echo "RPC_URL=$rpc_url" >> $GITHUB_ENV

      - name: Save RPC URL to file
        if: inputs.custom_run_id != ''
        run: |
          workflow_id=${{ inputs.custom_run_id }}
          echo "${{ env.RPC_URL }}" > rpc_url%${{ env.CLEAN_REF }}%${{ inputs.custom_run_id }}.txt

      - name: Upload RPC URL
        uses: actions/upload-artifact@v4
        if: inputs.custom_run_id != ''
        with:
          name: rpc-url___${{ env.CLEAN_REF }}___${{ inputs.custom_run_id }}
          path: rpc_url%${{ env.CLEAN_REF }}%${{ inputs.custom_run_id }}.txt
