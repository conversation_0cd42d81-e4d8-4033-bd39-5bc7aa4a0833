name: Trivy scanner

on:
  pull_request:
    branches: [master]
  push:
    branches: [master]
  schedule:
    - cron: '29 19 * * 4'
  workflow_dispatch:

permissions:
  contents: read

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      actions: read
    env:
      IMAGE_TAG: nethermind:${{ github.sha }}
    steps:
      - name: Check out repository
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 #v5.0.0

      - name: Build Docker image
        run: docker build -t $IMAGE_TAG .
      
      - name: <PERSON>an
        uses: aquasecurity/trivy-action@915b19bbe73b92a6cf82a1bc12b087c9a19a5fe2 #v0.28.0
        with:
          image-ref: ${{ env.IMAGE_TAG }}
          format: template
          template: '@/contrib/sarif.tpl'
          output: trivy-results.sarif
          severity: CRITICAL,HIGH
        env:
          TRIVY_DB_REPOSITORY: public.ecr.aws/aquasecurity/trivy-db

      - name: Upload scan results
        uses: github/codeql-action/upload-sarif@cf5b0a9041d3c1d336516f1944c96d96598193cc #v2.19.1
        with:
          sarif_file: trivy-results.sarif
