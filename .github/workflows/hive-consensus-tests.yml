name: "Hive consensus tests"

on:
  push:
    branches: [release/*]

  workflow_dispatch:
    inputs:
      parallelism:
        description: 'Number of concurrently running tests in each job.'
        required: true
        default: '8'
        type: choice
        options: ['1', '2', '3', '4', '8', '16']

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  create_docker_image:
    name: "Generate docker image"
    outputs:
      cleanRef: ${{ steps.prepare_ref.outputs.cleanRef }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          ref: ${{ github.ref }}

      - name: Authenticate App
        id: gh-app
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}

      - name: Prepare docker tag
        id: prepare_ref
        run: |
          REF_NAME=${{ github.ref }}
          CLEAN_REF=$(echo "${REF_NAME/refs\/heads\//}" | sed 's/[^a-zA-Z0-9._-]/-/g')
          echo "CLEAN_REF=$CLEAN_REF" >> $GITHUB_ENV
          echo "cleanRef=$CLEAN_REF" >> $GITHUB_OUTPUT

      - name: Set Repo and Org Variables
        id: cleanup
        run: |
          echo "ORG_NAME=${{ github.repository_owner }}" >> $GITHUB_ENV
          echo "REPO_NAME=${{ github.event.repository.name }}" >> $GITHUB_ENV

      - name: Check if master or release branch
        id: check_conditions
        run: |
          ref="${{ github.ref }}"
          event_name="${{ github.event_name }}"

          # Append "refs/heads/" prefix if it's not already there
          if [[ $ref != refs/heads/* ]]; then
            ref="refs/heads/$ref"
          fi

          # Initialize variables
          skip_docker_build="false"
          skip_wait_for_docker="false"

          # Set conditions based on branch and event type
          if [[ "$ref" == "refs/heads/master" || $ref == refs/heads/release* ]]; then
            skip_docker_build="true"

            if [[ "$event_name" == "workflow_dispatch" ]]; then
              skip_wait_for_docker="true"
            fi
          fi

          # Output the variables
          echo "skip_docker_build=$skip_docker_build" >> $GITHUB_OUTPUT
          echo "skip_wait_for_docker=$skip_wait_for_docker" >> $GITHUB_OUTPUT

      - name: Trigger Docker Build Action with Cleaned Ref
        if: steps.check_conditions.outputs.skip_docker_build != 'true'
        uses: benc-uk/workflow-dispatch@v1
        env:
          ADDITIONAL_OPTIONS: ${{ inputs.additional_options }}
        with:
          workflow: publish-docker.yml
          ref: "${{ github.ref }}"
          token: "${{ steps.gh-app.outputs.token }}"
          inputs: '{
            "tag": "${{ env.CLEAN_REF }}",
            "dockerfile": "Dockerfile",
            "build-config": "release"
            }'

      - name: Wait for Docker Build Action to complete
        if: steps.check_conditions.outputs.skip_wait_for_docker != 'true'
        env:
          GITHUB_TOKEN: ${{ steps.gh-app.outputs.token }}
          WORKFLOW_ID: 'publish-docker.yml'
          MAX_WAIT_MINUTES: '20'
          INTERVAL: '5'
          TIMEOUT: '20'
          ORG_NAME: ${{ env.ORG_NAME }}
          REPO_NAME: ${{ env.REPO_NAME }}
          REF: ${{ github.ref }}
        run: |
          chmod +x scripts/wait-for-workflow.sh
          ./scripts/wait-for-workflow.sh
        working-directory: ${{ github.workspace }}

  generate_hive_consensus_tests:
    name: "Prepare all hive tests to be started"
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    needs: [create_docker_image]
    steps:
      - name: Check out Nethermind repository
        uses: actions/checkout@v5
        with:
          path: nethermind
          submodules: "recursive"
      - name: Set up .NET
        uses: actions/setup-dotnet@v5
        with:
          global-json-file: nethermind/global.json
      - name: Generate Hive Json For Matrix
        id: set-matrix
        run: |
          cd nethermind/tools/HiveConsensusWorkflowGenerator
          dotnet run
          cat matrix.json
          echo "matrix=$(jq -c . matrix.json)" >> $GITHUB_OUTPUT

  run_hive_tests:
    runs-on: ubuntu-latest
    needs: [generate_hive_consensus_tests, create_docker_image]
    strategy:
      fail-fast: false
      matrix:
        hiveTests: ${{fromJson(needs.generate_hive_consensus_tests.outputs.matrix)}}
    steps:
      - name: Set up parameters
        run: |
          echo "PARALLELISM=${{ github.event.inputs.parallelism || '8' }}" >> $GITHUB_ENV

      - name: Check out Nethermind repository
        uses: actions/checkout@v5
        with:
          path: nethermind

      - name: Install Linux packages
        run: |
          sudo apt-get update
          sudo apt-get install libsnappy-dev libc6-dev libc6 build-essential

      - name: Set up Go environment
        uses: actions/setup-go@v6
        with:
          go-version: '>=1.17.0'

      - name: Check out Hive repository
        uses: actions/checkout@v5
        with:
          repository: ethereum/hive
          ref: master
          path: hive

      - name: Patch Hive Dockerfile
        run: |
          tag=${{ needs.create_docker_image.outputs.cleanRef }}
          sed -i -e "s|^ARG baseimage=.*|ARG baseimage=nethermindeth/nethermind|" -e "s|^ARG tag=.*|ARG tag=$tag|" hive/clients/nethermind/Dockerfile

      - name: Build Hive
        working-directory: hive
        run: go build .

      - name: Run hive for tests - ${{ join(matrix.hiveTests.testNames, ', ') }}
        continue-on-error: true
        working-directory: hive
        run: |
          IFS=',' read -ra TEST_NAMES <<< "${{ join(matrix.hiveTests.testNames, ',') }}"
          for testName in "${TEST_NAMES[@]}"
          do
            ./hive --client nethermind --sim ethereum/consensus --sim.limit /$testName --sim.parallelism $PARALLELISM
          done
      - name: Print results
        run: |
          chmod +x nethermind/scripts/hive-results.sh
          nethermind/scripts/hive-results.sh "hive/workspace/logs/*.json"
      # - name: Send results to dashboard
      #   uses: appleboy/scp-action@master
      #   with:
      #     host: ${{ secrets.HIVE_HOST }}
      #     username: ${{ secrets.HIVE_USERNAME }}
      #     key: ${{ secrets.HIVE_KEY }}
      #     port: ${{ secrets.HIVE_PORT }}
      #     source: hive/workspace/logs/*
      #     target: ${{ secrets.HIVE_DIR }}/
