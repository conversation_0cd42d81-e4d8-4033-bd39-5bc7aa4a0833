name: '[UPDATE] POA bootnodes'

on:
  repository_dispatch:
    types: update_bootnodes
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  update-bootnodes:
    name: Update POA bootnodes
    runs-on: ubuntu-latest
    steps:
    - name: Check out Nethermind repository
      uses: actions/checkout@v5
    - name: Check out poa-chain-spec repository
      uses: actions/checkout@v5
      with:
        repository: 'poanetwork/poa-chain-spec'
        path: poa-chain-spec
    - name: Update Poacore chainspec
      run: |
        cd poa-chain-spec/
        git checkout core
        cd ..
        cat src/Nethermind/Chains/poacore.json | jq ".nodes = $(cat poa-chain-spec/spec.json | jq ".nodes")" > src/Nethermind/Chains/poacore.json 
    - name: Update xDai chainspec
      run: |
        cd poa-chain-spec/
        git checkout dai
        cd ..
        cat src/Nethermind/Chains/xdai.json | jq ".nodes = $(cat poa-chain-spec/spec.json | jq ".nodes")" > src/Nethermind/Chains/xdai.json
    - name: Remove poa-chain-spec repo
      run: rm -rf poa-chain-spec/
    - name: Create pull request     
      uses: peter-evans/create-pull-request@v2.4.4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: Update bootnodes
        body: |
          Nethermind bootnodes have been updated.
          - Files included - poacore.json, xdai.json
          - Auto-generated by [create-pull-request][1]

          [1]: https://github.com/peter-evans/create-pull-request
        title: 'Updating Bootnodes'
        labels: bootnodes
        author-name: github-actions[bot]
        author-email: 41898282+github-actions[bot]@users.noreply.github.com
        committer-name: GitHub
        committer-email: <EMAIL>
        branch: bootnodes-update
        branch-suffix: short-commit-hash
        base: master
    - name: Check outputs
      run: |
        echo "Pull Request Number - ${{ env.PULL_REQUEST_NUMBER }}"
