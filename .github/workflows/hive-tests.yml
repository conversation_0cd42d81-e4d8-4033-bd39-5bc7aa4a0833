name: Hive tests

on:
  push:
    tags: ['*']
    branches: [master]
  workflow_dispatch:
    inputs:
      test-suite:
        description: Test suite
        type: choice
        options:
          - ''
          - devp2p
          - ethereum/consensus
          - ethereum/eest
          - ethereum/engine
          - ethereum/graphql
          - ethereum/rpc
          - ethereum/rpc-compat
          - ethereum/sync
      limit:
        description: Limit
        required: false
        type: string
      log-level:
        description: Log level
        required: true
        default: '3'
        type: choice
        options: ['0', '1', '2', '3', '4', '5']
      hive-repo:
        description: Hive repo
        required: true
        default: ethereum/hive
        type: string
      hive-branch:
        description: Hive branch
        required: false
        default: master
        type: string
      buildarg:
        description: Simulator build argument
        required: false
        type: string

jobs:
  test:
    name: Build and run tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        test-suite: ${{ fromJson(github.event.inputs.test-suite == '' &&
          '["devp2p","ethereum/engine","ethereum/graphql","ethereum/rpc","ethereum/rpc-compat","ethereum/sync"]' ||
          format('["{0}"]', github.event.inputs.test-suite)
          ) }}
    steps:
      - name: Set up parameters
        run: |
          echo "LIMIT=${{ github.event.inputs.limit || '' }}" >> $GITHUB_ENV
          echo "LOG_LEVEL=${{ github.event.inputs.log-level || '3' }}" >> $GITHUB_ENV
          echo "HIVE_REPO=${{ github.event.inputs.hive-repo || 'ethereum/hive' }}" >> $GITHUB_ENV
          echo "HIVE_BRANCH=${{ github.event.inputs.hive-branch || 'master' }}" >> $GITHUB_ENV
          echo "BUILDARG=${{ github.event.inputs.buildarg || '' }}" >> $GITHUB_ENV
      - name: Check out Nethermind repository
        uses: actions/checkout@v5
        with:
          path: nethermind
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build Docker image
        uses: docker/build-push-action@v6
        env:
          DOCKER_BUILD_SUMMARY: false
        with:
          context: nethermind
          file: nethermind/Dockerfile
          tags: nethermind:test-${{ github.sha }}
          outputs: type=docker,dest=/tmp/image.tar
      - name: Check out Hive repository
        uses: actions/checkout@v5
        with:
          repository: ${{ env.HIVE_REPO }}
          ref: ${{ env.HIVE_BRANCH }}
          path: hive
      - name: Patch Hive Dockerfile
        run: sed -i 's#FROM $baseimage:$tag#FROM nethermind:test-${{ github.sha }}#g' hive/clients/nethermind/Dockerfile
      - name: Build Hive
        working-directory: hive
        run: go build .
      - name: Load Docker image
        run: docker load --input /tmp/image.tar
      - name: Run Hive
        continue-on-error: true
        working-directory: hive
        run: |
          HIVE_ARGS="--client nethermind --sim ${{ matrix.test-suite }} --sim.loglevel $LOG_LEVEL"
          [[ -n "$LIMIT" ]] && HIVE_ARGS="$HIVE_ARGS --sim.limit $LIMIT"
          [[ -n "$BUILDARG" ]] && HIVE_ARGS="$HIVE_ARGS --sim.buildarg \"$BUILDARG\""
          ./hive $HIVE_ARGS
      - name: Upload results
        uses: actions/upload-artifact@v4
        with:
          name: results-${{ strategy.job-index }}-${{ github.run_number }}-${{ github.run_attempt }}
          path: hive/workspace
          retention-days: 7
      - name: Print results
        run: |
          rm hive/workspace/logs/hive.json
          nethermind/scripts/hive-results.sh "hive/workspace/logs/*.json"
