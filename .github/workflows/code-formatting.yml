name: Code formatting

on:
  pull_request:
  workflow_dispatch:

jobs:
  spacing-check:
    name: Check whitespaces
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Check out repository
        uses: actions/checkout@v5
      - name: Set up .NET
        uses: actions/setup-dotnet@v5
      - name: Format
        run: dotnet format whitespace src/Nethermind/ --folder --verify-no-changes
