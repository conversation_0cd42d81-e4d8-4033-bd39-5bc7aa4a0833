name: Update docs

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      ref:
        description: Branch or tag
        required: true
        default: master

jobs:
  update-docs:
    name: Update docs
    runs-on: ubuntu-latest
    steps:
      - name: Create GitHub app token
        id: gh-app
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
          repositories: |
            nethermind
            docs
      - name: Check out Nethermind repository
        uses: actions/checkout@v5
        with:
          ref: ${{ github.event.inputs.ref || github.ref }}
          path: n
      - name: Check out Nethermind docs repository
        uses: actions/checkout@v5
        with:
          repository: NethermindEth/docs
          path: d
          token: ${{ steps.gh-app.outputs.token }}
      - name: Set up .NET
        uses: actions/setup-dotnet@v5
        with:
          global-json-file: n/global.json
      - name: Build DocGen
        working-directory: n
        run: dotnet build tools/DocGen/DocGen.csproj -c release -o DocGen
      - name: Generate docs
        run: n/DocGen/DocGen $GITHUB_WORKSPACE/d --config --jsonrpc --metrics
      - name: Tag a new version
        if: github.event_name == 'release' && !github.event.release.prerelease
        working-directory: d
        run: |
          npm i
          npm run docusaurus docs:version ${{ github.event.release.tag_name }}
      - name: Create a pull request
        working-directory: d
        env:
          GH_TOKEN: ${{ steps.gh-app.outputs.token }}
        run: |
          version="${{ github.event_name == 'release' && format(' of v{0}', github.event.release.tag_name) || '' }}"
          head_branch=feature/auto-update-${{ github.run_number }}-${{ github.run_attempt }}
          git config user.name "${{ github.actor }}"
          git config user.email "${{ github.actor }}@users.noreply.github.com"
          git checkout -b $head_branch
          git add -A
          git commit -am "Auto-update docs"
          git push origin $head_branch
          gh pr create -B main -H $head_branch -t "Auto-update docs" \
            -b "Autogenerated docs on \`${{ github.event_name }}\` event${version}." -l docgen
