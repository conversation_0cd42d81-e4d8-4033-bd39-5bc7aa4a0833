name: Publish Docker image

on:
  push:
    branches: [master, release/*, paprika]
    paths: [src/Nethermind/**]

  workflow_dispatch:
    inputs:
      image-name:
        description: Image name
        required: true
        default: nethermind
      tag:
        description: Image tag
        required: true
      dockerfile:
        description: Dockerfile
        required: true
        default: Dockerfile
      build-config:
        description: Build configuration
        required: true
        default: release
        type: choice
        options: [release, debug]

jobs:
  publish-docker:
    name: Publish to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository
        uses: actions/checkout@v5
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}
      - name: Build and push image to Docker Hub (staging)
        run: |
          branch=$(echo "${{ github.ref }}" | sed -e "s/refs\/heads\///g")
          tag=$(echo "${{ github.event.inputs.tag || '$branch' }}" | sed 's/\//-/g') # replace '/' with '-'
          image_name=nethermindeth/${{ github.event.inputs.image-name || 'nethermind' }}
          build_timestamp=$(date '+%s')
          
          echo "Building image with tag $tag"

          docker buildx build --platform=linux/amd64,linux/arm64 \
            -f ${{ github.event.inputs.dockerfile || 'Dockerfile' }} \
            -t "$image_name:$tag" \
            ${{ endsWith(github.ref, '/master') && github.event_name == 'push' && '-t $image_name:master-${GITHUB_SHA:0:7}' || '' }} \
            --build-arg BUILD_CONFIG=${{ github.event.inputs.build-config || 'release' }} \
            --build-arg BUILD_TIMESTAMP=$build_timestamp \
            --build-arg CI=$CI \
            --build-arg COMMIT_HASH=$GITHUB_SHA \
            . --push
