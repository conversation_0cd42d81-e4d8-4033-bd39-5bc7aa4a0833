name: Assertoor tests

on:
  workflow_dispatch:
  workflow_run:
    workflows: ["Publish Docker image"]
    branches: [master]
    types:
      - completed

jobs:
  get_tests:
    name: "Run assertoor tests on Nethermind pairs"
    runs-on: ubuntu-latest
    steps:
    - name: Authenticate App
      id: gh-app
      uses: actions/create-github-app-token@v2
      with:
        app-id: ${{ vars.APP_ID }}
        private-key: ${{ secrets.APP_PRIVATE_KEY }}
        
    - name: Checkout this repository
      uses: actions/checkout@v5
      with:
        path: main-repo

    - name: Checkout assertoor-test repository
      uses: actions/checkout@v5
      with:
        repository: kamilchodola/assertoor-test
        path: assertoor-test
        
    - name: Set up Python
      uses: actions/setup-python@v6.1.1
      with:
        python-version: '3.x'

    - name: Install dependencies
      run: pip install requests beautifulsoup4

    - name: Run retriveAllTests.py script
      run: |
        python assertoor-test/scripts/retriveAllTests/retriveAllTests.py --exclude "pectra-dev" "verkle" "validator-lifecycle" "slashing" "mev" > /tmp/output.txt
        cat /tmp/output.txt
        chmod 777 /tmp/output.txt
        echo "ASSERTOOR_PARAMS_FILE=/tmp/output.txt" >> $GITHUB_ENV

    - name: Prepare docker tag
      id: prepare_ref
      run: |
        REF_NAME=${{ inputs.nethermind_repo_ref || github.ref }}
        CLEAN_REF=$(echo "${REF_NAME/refs\/heads\//}" | sed 's/[^a-zA-Z0-9._-]/-/g')
        echo "CLEAN_REF=$CLEAN_REF" >> $GITHUB_ENV

    - name: Set Repo and Org Variables
      run: |
        echo "ORG_NAME=${{ github.repository_owner }}" >> $GITHUB_ENV
        echo "REPO_NAME=${{ github.event.repository.name }}" >> $GITHUB_ENV
        
    - name: Check if master branch and default additional_options
      id: check_conditions
      run: |
        ref="${{ github.ref }}"
        
        if [[ $ref != refs/heads/* ]]; then
          ref="refs/heads/$ref"
        fi

        if 
        [[ "$ref" == "refs/heads/master" ]]; then
          echo "skip_docker_build=true" >> $GITHUB_OUTPUT
        else
          echo "skip_docker_build=false" >> $GITHUB_OUTPUT
        fi

    - name: Trigger Docker Build Action with Cleaned Ref
      if: steps.check_conditions.outputs.skip_docker_build != 'true'
      uses: benc-uk/workflow-dispatch@v1
      with:
          workflow: publish-docker.yml
          ref: "${{ github.ref }}"
          token: "${{ steps.gh-app.outputs.token }}"
          inputs: '{
              "tag": "${{ env.CLEAN_REF }}",
              "dockerfile": "${{ steps.extract_dockerfile.outputs.dockerfile }}",
              "build-config": "${{ steps.extract_dockerfile.outputs.build-config }}"
           }'
        
    - name: Wait for Docker Build Action to complete
      if: steps.check_conditions.outputs.skip_docker_build != 'true'
      env:
        GITHUB_TOKEN: ${{ steps.gh-app.outputs.token }}
        WORKFLOW_ID: 'publish-docker.yml'
        MAX_WAIT_MINUTES: "5"
        INTERVAL: "5"
        TIMEOUT: '20'
        ORG_NAME: ${{ env.ORG_NAME }}
        REPO_NAME: ${{ env.REPO_NAME }}
        REF: ${{ github.ref }}
      run: |
        chmod +x main-repo/scripts/wait-for-workflow.sh
        ./main-repo/scripts/wait-for-workflow.sh
      working-directory: ${{ github.workspace }}
        
    - name: Replace Placeholders in YAML
      run: |
        assertoor_params=$(cat ${{ env.ASSERTOOR_PARAMS_FILE}})
        sed -i "s|<DOCKER_IMAGE_PLACEHOLDER>|nethermindeth/nethermind:${{ env.CLEAN_REF }}|g" main-repo/scripts/config/assertoor.yml
        sed -i "/<ASSERTOOR_PARAMS_PLACEHOLDER>/ {
          r /tmp/output.txt
          d
        }" main-repo/scripts/config/assertoor.yml
        cat main-repo/scripts/config/assertoor.yml
      
    - name: Kurtosis Assertoor GitHub Action
      uses: ethpandaops/kurtosis-assertoor-github-action@v1
      with:
        ethereum_package_url: "github.com/ethpandaops/ethereum-package"
        ethereum_package_branch: ""
        ethereum_package_args: main-repo/scripts/config/assertoor.yml
