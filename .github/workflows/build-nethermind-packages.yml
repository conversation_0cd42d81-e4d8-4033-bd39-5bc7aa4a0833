name: Build Nethermind packages

on:
  workflow_dispatch:

jobs:
  build:
    name: Build Nethermind packages
    runs-on: ubuntu-latest
    env:
      PACKAGE_DIR: pkg
      PACKAGE_RETENTION: 7
      PUB_DIR: pub
      SCRIPTS_PATH: ${{ github.workspace }}/scripts/build
    steps:
      - name: Check out repository
        uses: actions/checkout@v5
      - name: Set up .NET
        uses: actions/setup-dotnet@v5
      - name: Build Nethermind.Runner
        id: build
        run: |
          build_timestamp=$(date '+%s')
          echo "build-timestamp=$build_timestamp" >> $GITHUB_OUTPUT
          echo "commit-hash=${GITHUB_SHA:0:8}" >> $GITHUB_OUTPUT
          $SCRIPTS_PATH/build.sh $GITHUB_SHA $build_timestamp
      - name: Archive packages
        env:
          PACKAGE_PREFIX: nethermind-preview-${{ steps.build.outputs.commit-hash }}
        run: |
          echo "PACKAGE_PREFIX=$PACKAGE_PREFIX" >> $GITHUB_ENV
          $SCRIPTS_PATH/archive.sh
      - name: Upload Nethermind Linux x64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PACKAGE_PREFIX }}-linux-x64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*linux-x64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind Linux arm64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PACKAGE_PREFIX }}-linux-arm64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*linux-arm64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind Windows x64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PACKAGE_PREFIX }}-windows-x64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*windows-x64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind macOS x64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PACKAGE_PREFIX }}-macos-x64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*macos-x64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind macOS arm64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PACKAGE_PREFIX }}-macos-arm64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*macos-arm64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
