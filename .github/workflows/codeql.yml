name: CodeQL analysis

on:
  push:
    tags: ['*']
  schedule:
    - cron: '0 0 * * 0'
  workflow_dispatch:

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      fail-fast: false
      matrix:
        language: ['csharp', 'actions']
    steps:
      - name: Check out repository
        uses: actions/checkout@v5
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          queries: security-and-quality
          packs: githubsecuritylab/codeql-csharp-queries
      - name: Set up .NET
        uses: actions/setup-dotnet@v5
      - name: Build Nethermind
        working-directory: src/Nethermind
        run: dotnet build Nethermind.slnx -c release
      - name: Perform CodeQL analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: '/language:${{ matrix.language }}'
