name: '[RUN] Benchmark Performance Tests'

on:
  push:
    branches: [ master ]
  workflow_dispatch:
  

jobs:
  run-benchmarks:
    if: github.repository_owner == 'NethermindEth'
    name: Triggering Nethermind Benchmark Performance Run
    runs-on: ubuntu-latest
    steps:
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: eu-central-1
    - name: Deploy Amazon EC2 instance for Benchmarking
      uses: aws-actions/aws-cloudformation-github-deploy@v1
      with:
        name: Benchmark-${{ github.sha }}
        template: https://cf-templates-1tzgi56hs2v4n-eu-central-1.s3.eu-central-1.amazonaws.com/performance-test.yaml
        parameter-overrides: "CommitSha=${{ github.sha }}"
