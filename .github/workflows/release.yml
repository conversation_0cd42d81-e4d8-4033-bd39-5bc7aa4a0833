name: Release

on:
  workflow_dispatch:
    inputs:
      tag:
        description: Version tag
        required: true

permissions:
  deployments: write
  packages: write
  contents: write

env:
  PACKAGE_DIR: pkg
  PACKAGE_RETENTION: 7
  PUB_DIR: pub
  SCRIPTS_PATH: ${{ github.workspace }}/scripts/build

jobs:
  build:
    name: Build Nethermind packages
    runs-on: ubuntu-latest
    outputs:
      build-timestamp: ${{ steps.build.outputs.build-timestamp }}
      package-prefix: ${{ steps.archive.outputs.package-prefix }}
      prerelease: ${{ steps.build.outputs.prerelease }}
    steps:
      - name: Check out repository
        uses: actions/checkout@v5
      - name: Set up .NET
        uses: actions/setup-dotnet@v5
      - name: Build Nethermind.Runner
        id: build
        run: |
          build_timestamp=$(date '+%s')
          echo "build-timestamp=$build_timestamp" >> $GITHUB_OUTPUT
          echo "commit-hash=${GITHUB_SHA:0:8}" >> $GITHUB_OUTPUT
          echo "prerelease=${{ contains(github.event.inputs.tag, '-') }}" >> $GITHUB_OUTPUT
          $SCRIPTS_PATH/build.sh $GITHUB_SHA $build_timestamp
      - name: Archive packages
        id: archive
        env:
          PACKAGE_PREFIX: nethermind-${{ github.event.inputs.tag }}-${{ steps.build.outputs.commit-hash }}
        run: |
          echo "package-prefix=$PACKAGE_PREFIX" >> $GITHUB_OUTPUT
          $SCRIPTS_PATH/archive.sh
      - name: Upload Nethermind Linux x64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.archive.outputs.package-prefix }}-linux-x64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*linux-x64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind Linux arm64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.archive.outputs.package-prefix }}-linux-arm64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*linux-arm64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind Windows x64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.archive.outputs.package-prefix }}-windows-x64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*windows-x64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind macOS x64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.archive.outputs.package-prefix }}-macos-x64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*macos-x64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind macOS arm64 package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.archive.outputs.package-prefix }}-macos-arm64-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*macos-arm64*
          retention-days: ${{ env.PACKAGE_RETENTION }}
      - name: Upload Nethermind reference assemblies
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.archive.outputs.package-prefix }}-ref-assemblies-package
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}/*ref-assemblies*
          retention-days: ${{ env.PACKAGE_RETENTION }}

  approval:
    name: Approve
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: Releases
      url: https://github.com/NethermindEth/nethermind/releases/tag/${{ github.event.inputs.tag }}
    steps:
      - name: Wait for approval
        run: echo "Waiting for approval..."

  publish-github:
    name: Publish to GitHub
    runs-on: ubuntu-latest
    needs: [approval, build]
    steps:
      - name: Check out Nethermind repository
        uses: actions/checkout@v5
      - name: Authenticate App
        id: gh-app
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}
      - name: Publish
        env:
          GIT_TAG: ${{ github.event.inputs.tag }}
          GITHUB_TOKEN: ${{ steps.gh-app.outputs.token }}
          PACKAGE_PREFIX: ${{ needs.build.outputs.package-prefix }}
          PRERELEASE: ${{ needs.build.outputs.prerelease }}
        run: |
          cp $GITHUB_WORKSPACE/$PACKAGE_DIR/**/*.zip $GITHUB_WORKSPACE/$PACKAGE_DIR
          rm -rf $GITHUB_WORKSPACE/$PACKAGE_DIR/*/
          $SCRIPTS_PATH/publish-github.sh

  publish-downloads:
    name: Publish to Downloads page
    runs-on: ubuntu-latest
    needs: [approval, build]
    if: needs.build.outputs.prerelease == 'false'
    steps:
      - name: Check out Nethermind repository
        uses: actions/checkout@v5
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: ${{ github.workspace }}/${{ env.PACKAGE_DIR }}
      - name: Configure GPG Key
        run: |
          mkdir -p ~/.gnupg/
          printf "${{ secrets.GPG_SIGNING_KEY }}" | base64 --decode > ~/.gnupg/private.key
          gpg --import --no-tty --batch --yes ~/.gnupg/private.key
      - name: Publish packages to Downloads page
        env:
          DOWNLOADS_PAGE: ${{ secrets.DOWNLOADS_API_KEY }}
          PACKAGE_PREFIX: ${{ needs.build.outputs.package-prefix }}
          PASS: ${{ secrets.GPG_PASSWORD }}
        run: |
          cp $GITHUB_WORKSPACE/$PACKAGE_DIR/**/*.zip $GITHUB_WORKSPACE/$PACKAGE_DIR
          rm -rf $GITHUB_WORKSPACE/$PACKAGE_DIR/*/
          $SCRIPTS_PATH/publish-downloads.sh

  publish-docker:
    name: Publish to Docker Hub
    runs-on: ubuntu-latest
    needs: [approval, build]
    steps:
      - name: Check out Nethermind repository
        uses: actions/checkout@v5
      - name: Authenticate App
        id: gh-app
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_ACCESS_TOKEN }}
      - name: Build and push image to Docker Hub
        run: |
          image=nethermind/nethermind

          for suffix in "" ".chiseled"; do
            if [[ $suffix == ".chiseled" ]]; then tag_suffix="-chiseled"; else tag_suffix=""; fi

            docker buildx build --platform=linux/amd64,linux/arm64 -f Dockerfile$suffix \
              ${{ needs.build.outputs.prerelease == 'false' && '-t $image:latest$tag_suffix' || '' }} \
              -t "$image:${{ github.event.inputs.tag }}$tag_suffix" \
              --build-arg BUILD_TIMESTAMP=${{ needs.build.outputs.build-timestamp }} \
              --build-arg CI=$CI \
              --build-arg COMMIT_HASH=$GITHUB_SHA \
              . --push
          done
