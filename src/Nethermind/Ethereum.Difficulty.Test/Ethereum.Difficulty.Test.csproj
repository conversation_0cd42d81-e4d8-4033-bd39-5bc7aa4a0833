<Project Sdk="Microsoft.NET.Sdk">
  
  <Import Project="../tests.props" />
  
  <ItemGroup>
    <ProjectReference Include="..\Ethereum.Test.Base\Ethereum.Test.Base.csproj" />
    <ProjectReference Include="..\Nethermind.Blockchain\Nethermind.Blockchain.csproj" />
    <ProjectReference Include="..\Nethermind.Core\Nethermind.Core.csproj" />
    <EmbeddedResource Include="..\..\tests\BasicTests\difficulty*.json">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
</Project>
