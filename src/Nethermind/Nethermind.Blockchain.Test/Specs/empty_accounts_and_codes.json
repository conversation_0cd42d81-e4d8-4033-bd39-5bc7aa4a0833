{"version": "1", "engine": {"Ethash": {"params": {"minimumDifficulty": "0x20000", "difficultyBoundDivisor": "0x800", "durationLimit": "0x0d", "homesteadTransition": "0x0", "eip100bTransition": "0x0", "daoHardforkBeneficiary": "******************************************", "blockReward": {"0x0": "0x1BC16D674EC80000"}, "difficultyBombDelays": {"0x0": 2000000}}}}, "params": {"eip150Transition": "0x0", "eip160Transition": "0x0", "eip161abcTransition": "0x0", "eip161dTransition": "0x0", "eip155Transition": "0x0", "maxCodeSizeTransition": "0x0", "maxCodeSize": 24576, "eip140Transition": "0x0", "eip211Transition": "0x0", "eip214Transition": "0x0", "eip658Transition": "0x0", "eip145Transition": "0x0", "eip1014Transition": "0x0", "eip1052Transition": "0x0", "eip1283Transition": "0x0", "eip1283DisableTransition": "0x0", "eip152Transition": "0x0", "eip1108Transition": "0x0", "eip1344Transition": "0x0", "eip1884Transition": "0x0", "eip2028Transition": "0x0", "eip2200Transition": "0x0", "eip2565Transition": "0x0", "eip2718Transition": "0x0", "eip2929Transition": "0x0", "eip2930Transition": "0x0", "chainID": "0x1"}, "genesis": {"seal": {"ethereum": {"nonce": "0x0000000000000000", "mixHash": "0x0000000000000000000000000000000000000000000000000000000000000000"}}, "difficulty": "0x20000", "author": "******************************************", "timestamp": "0x0", "parentHash": "0x0000000000000000000000000000000000000000000000000000000000000000", "extraData": "0x00", "gasLimit": "0x0bebc200"}, "accounts": {"******************************************": {"balance": "0x00", "code": "0x7c0100000000000000000000000000000000000000000000000000000000600035046397dd3054811415610065576004356040526024356060526040516060515b808212156100625760006000600060006000866000f150600182019150610040565b50505b50", "nonce": "0x00", "storage": {}}, "******************************************": {"balance": "0x0de0b6b3a7640000", "code": "0x", "nonce": "0x01", "storage": {}, "******************************************": {"builtin": {"name": "ecrecover", "pricing": {"linear": {"base": 3000, "word": 0}}}}, "******************************************": {"builtin": {"name": "sha256", "pricing": {"linear": {"base": 60, "word": 12}}}}, "0x0000000000000000000000000000000000000003": {"builtin": {"name": "ripemd160", "pricing": {"linear": {"base": 600, "word": 120}}}}, "0x0000000000000000000000000000000000000004": {"builtin": {"name": "identity", "pricing": {"linear": {"base": 15, "word": 3}}}}, "0x0000000000000000000000000000000000000005": {"builtin": {"name": "modexp", "activate_at": "0x0", "pricing": {"modexp": {"divisor": 20}}}}, "0x0000000000000000000000000000000000000006": {"builtin": {"name": "alt_bn128_add", "activate_at": "0x0", "pricing": {"linear": {"base": 500, "word": 0}}}}, "0x0000000000000000000000000000000000000007": {"builtin": {"name": "alt_bn128_mul", "activate_at": "0x0", "pricing": {"linear": {"base": 40000, "word": 0}}}}, "0x0000000000000000000000000000000000000008": {"builtin": {"name": "alt_bn128_pairing", "activate_at": "0x0", "pricing": {"alt_bn128_pairing": {"base": 100000, "pair": 80000}}}}, "0x0000000000000000000000000000000000000009": {"builtin": {"name": "blake2_f", "activate_at": "0x0", "pricing": {"blake2_f": {"gas_per_round": 1}}}}}}}