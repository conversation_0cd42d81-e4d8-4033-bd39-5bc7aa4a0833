{"engine": {"Ethash": {"params": {"minimumDifficulty": "0x20000", "difficultyBoundDivisor": "0x800", "durationLimit": "0xd", "blockReward": {"0x0": "0x1BC16D674EC80000"}, "homesteadTransition": "0x0", "eip100bTransition": "0x0", "difficultyBombDelays": {}}}}, "params": {"gasLimitBoundDivisor": "0x400", "registrar": "******************************************", "accountStartNonce": "0x0", "maximumExtraDataSize": "0x20", "minGasLimit": "0x1388", "networkID": "0x00146A2E", "MergeForkIdTransition": "0x0", "maxCodeSize": "0x6000", "maxCodeSizeTransition": "0x0", "eip150Transition": "0x0", "eip158Transition": "0x0", "eip160Transition": "0x0", "eip161abcTransition": "0x0", "eip161dTransition": "0x0", "eip155Transition": "0x0", "eip140Transition": "0x0", "eip211Transition": "0x0", "eip214Transition": "0x0", "eip658Transition": "0x0", "eip145Transition": "0x0", "eip1014Transition": "0x0", "eip1052Transition": "0x0", "eip1283Transition": "0x0", "eip1283DisableTransition": "0x0", "eip152Transition": "0x0", "eip1108Transition": "0x0", "eip1344Transition": "0x0", "eip1884Transition": "0x0", "eip2028Transition": "0x0", "eip2200Transition": "0x0", "eip2565Transition": "0x0", "eip2929Transition": "0x0", "eip2930Transition": "0x0", "eip1559Transition": "0x0", "eip3198Transition": "0x0", "eip3529Transition": "0x0", "eip3541Transition": "0x0", "eip3540TransitionTimestamp": "0x0", "eip3651TransitionTimestamp": "0x0", "eip3670TransitionTimestamp": "0x0", "eip3675TransitionTimestamp": "0x0", "eip3855TransitionTimestamp": "0x0", "eip3860TransitionTimestamp": "0x0", "eip4895TransitionTimestamp": "0x0", "terminalTotalDifficulty": "0x0"}, "genesis": {"seal": {"ethereum": {"nonce": "0x1234", "mixHash": "******************************************000000000000000000000000"}}, "difficulty": "0x01", "author": "******************************************", "timestamp": "0x63585F88", "parentHash": "******************************************000000000000000000000000", "extraData": "", "gasLimit": "0x400000"}, "accounts": {"******************************************": {"balance": "1"}}, "nodes": []}