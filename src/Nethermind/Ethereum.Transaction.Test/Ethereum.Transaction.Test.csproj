<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="../tests.props" />
  
  <ItemGroup>
    <ProjectReference Include="..\Nethermind.Blockchain\Nethermind.Blockchain.csproj" />
    <ProjectReference Include="..\Nethermind.Consensus\Nethermind.Consensus.csproj" />
    <ProjectReference Include="..\Nethermind.Core\Nethermind.Core.csproj" />
    <Content Include="..\..\tests\TransactionTests\**\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>
