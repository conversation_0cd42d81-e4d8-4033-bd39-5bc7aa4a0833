<Project Sdk="Microsoft.NET.Sdk">
  
  <Import Project="../tests.props" />
  
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.DataProtection.Extensions" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="..\..\tests\GeneralStateTests\st*\**\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\GeneralStateTests\Cancun\st*\**\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\GeneralStateTests\Shanghai\st*\**\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\EIPTests\StateTests\st*\**\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Ethereum.Test.Base\Ethereum.Test.Base.csproj" />
    <ProjectReference Include="..\Nethermind.Blockchain\Nethermind.Blockchain.csproj" />
    <ProjectReference Include="..\Nethermind.Core\Nethermind.Core.csproj" />
    <ProjectReference Include="..\Nethermind.Evm\Nethermind.Evm.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Blockhash\blockhash.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
