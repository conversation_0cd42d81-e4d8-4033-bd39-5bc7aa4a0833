// SPDX-FileCopyrightText: 2024 Demerzel Solutions Limited
// SPDX-License-Identifier: LGPL-3.0-only

using System.Collections.Generic;
using Ethereum.Test.Base;
using NUnit.Framework;

namespace Ethereum.Blockchain.Test
{
    // [TestFixture]
    // [Parallelizable(ParallelScope.All)]
    // public class Eip2537Tests : GeneralStateTestBase
    // {
    //     [TestCaseSource(nameof(LoadTests))]
    //     public void Test(GeneralStateTest test)
    //     {
    //         Assert.That(RunTest(test).Pass, Is.True);
    //     }
    //
    //     public static IEnumerable<GeneralStateTest> LoadTests()
    //     {
    //         var loader = new TestsSourceLoader(new LoadGeneralStateTestsStrategy(), "stEIP2537");
    //         return loader.LoadTests<GeneralStateTest>();
    //     }
    // }
}
