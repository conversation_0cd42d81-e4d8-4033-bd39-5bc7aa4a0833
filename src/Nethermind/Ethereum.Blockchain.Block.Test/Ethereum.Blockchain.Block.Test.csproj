<Project Sdk="Microsoft.NET.Sdk">
  
  <Import Project="../tests.props" />

  <ItemGroup>
    <Content Include="..\..\tests\BlockchainTests\ValidBlocks\bc*\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="..\..\tests\BlockchainTests\InvalidBlocks\bc*\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Ethereum.Test.Base\Ethereum.Test.Base.csproj" />
  </ItemGroup>
</Project>
