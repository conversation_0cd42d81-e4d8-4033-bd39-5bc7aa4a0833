// SPDX-FileCopyrightText: 2023 Demerzel Solutions Limited
// SPDX-License-Identifier: LGPL-3.0-only

using System.Collections.Generic;
using System.Linq;
using Ethereum.Test.Base;
using FluentAssertions;
using NUnit.Framework;

namespace Ethereum.Blockchain.Pyspec.Test;

[TestFixture]
[Parallelizable(ParallelScope.All)]
public class OsakaStateTests : GeneralStateTestBase
{
    [TestCaseSource(nameof(LoadTests))]
    public void Test(GeneralStateTest test) => RunTest(test).Pass.Should().BeTrue();

    private static IEnumerable<TestCaseData> LoadTests()
    {
        return [];
        // EOF tests
        //
        //TestsSourceLoader loader = new(new LoadPyspecTestsStrategy()
        //{
        //    ArchiveName = "fixtures_eip7692.tar.gz",
        //    ArchiveVersion = "eip7692@v2.3.0"
        //}, $"fixtures/state_tests/osaka");
        //return loader.LoadTests<GeneralStateTest>().Select(t => new TestCaseData(t)
        //    .SetName(t.Name)
        //    .SetCategory(t.Category));
    }
}
