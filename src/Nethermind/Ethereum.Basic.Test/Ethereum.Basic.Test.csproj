<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="../tests.props" />
  
  <ItemGroup>
    <EmbeddedResource Include="..\..\tests\GenesisTests\basic_genesis_tests.json">
      <Link>basic_genesis_tests.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\BasicTests\txtest.json">
      <Link>txtest.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Ethereum.Test.Base\Ethereum.Test.Base.csproj" />
    <ProjectReference Include="..\Nethermind.Core\Nethermind.Core.csproj" />
  </ItemGroup>
</Project>
