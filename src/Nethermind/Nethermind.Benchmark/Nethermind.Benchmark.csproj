<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <RootNamespace>Nethermind.Benchmarks</RootNamespace>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet" />
    <PackageReference Include="HexMate" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Nethermind.Consensus.Ethash\Nethermind.Consensus.Ethash.csproj" />
    <ProjectReference Include="..\Nethermind.Core.Test\Nethermind.Core.Test.csproj" />
    <ProjectReference Include="..\Nethermind.Core\Nethermind.Core.csproj" />
    <ProjectReference Include="..\Nethermind.Network.Stats\Nethermind.Network.Stats.csproj" />
  </ItemGroup>
</Project>
