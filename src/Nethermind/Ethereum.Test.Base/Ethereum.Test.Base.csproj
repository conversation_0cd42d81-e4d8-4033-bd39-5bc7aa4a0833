<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <IsPackable>false</IsPackable>
    <Nullable>annotations</Nullable>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit.Analyzers">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="NUnit3TestAdapter" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Nethermind.Blockchain\Nethermind.Blockchain.csproj" />
    <ProjectReference Include="..\Nethermind.Consensus.Ethash\Nethermind.Consensus.Ethash.csproj" />
    <ProjectReference Include="..\Nethermind.Consensus\Nethermind.Consensus.csproj" />
    <ProjectReference Include="..\Nethermind.Core.Test\Nethermind.Core.Test.csproj" />
    <ProjectReference Include="..\Nethermind.Core\Nethermind.Core.csproj" />
    <ProjectReference Include="..\Nethermind.Evm\Nethermind.Evm.csproj" />
  </ItemGroup>
</Project>
