<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <Nullable>annotations</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Nethermind.Abi\Nethermind.Abi.csproj" />
    <ProjectReference Include="..\Nethermind.Core\Nethermind.Core.csproj" />
    <ProjectReference Include="..\Nethermind.Db\Nethermind.Db.csproj" />
    <ProjectReference Include="..\Nethermind.Evm.Precompiles\Nethermind.Evm.Precompiles.csproj" />
    <ProjectReference Include="..\Nethermind.Evm\Nethermind.Evm.csproj" />
    <ProjectReference Include="..\Nethermind.Network.Stats\Nethermind.Network.Stats.csproj" />
    <ProjectReference Include="..\Nethermind.Specs\Nethermind.Specs.csproj" />
    <ProjectReference Include="..\Nethermind.State\Nethermind.State.csproj" />
    <ProjectReference Include="..\Nethermind.TxPool\Nethermind.TxPool.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.ClearScript.V8.Native.linux-arm64" />
    <PackageReference Include="Microsoft.ClearScript.V8.Native.linux-x64" />
    <PackageReference Include="Microsoft.ClearScript.V8.Native.osx-arm64" />
    <PackageReference Include="Microsoft.ClearScript.V8.Native.osx-x64" />
    <PackageReference Include="Microsoft.ClearScript.V8.Native.win-x64" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Polly" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Data\JSTracers\*.js">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </None>
  </ItemGroup>
</Project>
