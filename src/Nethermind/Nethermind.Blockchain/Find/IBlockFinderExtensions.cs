// SPDX-FileCopyrightText: 2022 Demerzel Solutions Limited
// SPDX-License-Identifier: LGPL-3.0-only

using System;
using Nethermind.Core;
using Nethermind.Core.Crypto;

namespace Nethermind.Blockchain.Find
{
    // ReSharper disable once InconsistentNaming
    public static class IBlockFinderExtensions
    {
        public static BlockHeader? FindParentHeader(this IBlockFinder finder, BlockHeader header, BlockTreeLookupOptions options)
        {
            return finder.FindHeader(header.ParentHash, options, blockNumber: header.Number - 1);
        }

        public static Block? FindParent(this IBlockFinder finder, Block block, BlockTreeLookupOptions options)
        {
            if (block.Header.ParentHash is null)
            {
                throw new InvalidOperationException(
                    $"Cannot find parent when parent hash is null on block with hash {block.Hash}.");
            }

            return finder.FindBlock(block.Header.ParentHash, options, blockNumber: block.Header.Number - 1);
        }

        public static Block? FindParent(this IBlockFinder finder, BlockHeader blockHeader, BlockTreeLookupOptions options)
        {
            if (blockHeader.ParentHash is null)
            {
                throw new InvalidOperationException(
                    $"Cannot find parent when parent hash is null on block with hash {blockHeader.Hash}.");
            }

            return finder.FindBlock(blockHeader.ParentHash, options, blockNumber: blockHeader.Number - 1);
        }

        public static Block? RetrieveHeadBlock(this IBlockFinder finder)
        {
            Hash256? headHash = finder.Head?.Hash;
            return headHash is null ? null : finder.FindBlock(headHash, BlockTreeLookupOptions.None);
        }
    }
}
