// SPDX-FileCopyrightText: 2024 Demerzel Solutions Limited
// SPDX-License-Identifier: LGPL-3.0-only

using Nethermind.Core;
using Nethermind.Core.Crypto;
using Nethermind.Core.Specs;

namespace Nethermind.Blockchain.Blocks;

public interface IBlockhashStore
{
    public void ApplyBlockhashStateChanges(BlockHeader blockHeader);
    public void ApplyBlockhashStateChanges(BlockHeader blockHeader, IReleaseSpec spec);
    public Hash256? GetBlockHashFromState(BlockHeader currentBlockHeader, long requiredBlockNumber);
    public Hash256? GetBlockHashFromState(<PERSON>Head<PERSON> currentBlockHeader, long requiredBlockNumber, IReleaseSpec? spec);
}
