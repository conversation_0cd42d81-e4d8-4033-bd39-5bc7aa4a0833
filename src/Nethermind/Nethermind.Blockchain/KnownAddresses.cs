// SPDX-FileCopyrightText: 2022 Demerzel Solutions Limited
// SPDX-License-Identifier: LGPL-3.0-only

using System.Collections.Generic;
using Nethermind.Core;

namespace Nethermind.Blockchain
{
    public static class KnownAddresses
    {
        public static string GetDescription(Address _) => "?";

        public static Dictionary<Address, string> KnownMiners = new()
        {
            { new Address("******************************************"), "2Miners: SOLO" },
            { new Address("******************************************"), "xnpool" },
            { new Address("******************************************"), "zhizhu.top" },
            { new Address("******************************************"), "Mining Express" },
            { new Address("******************************************"), "PandaMiner" },
            { new Address("******************************************"), "DwarfPool 1" },
            { new Address("******************************************"), "firepool" },
            { new Address("******************************************"), "W Pool" },
            { new Address("******************************************"), "FKPool" },
            { new Address("******************************************"), "Ethpool 2" },
            { new Address("******************************************"), "Hiveon Pool" },
            { new Address("******************************************"), "Nanopool" },
            { new Address("******************************************"), "Spark Pool" },
            { new Address("******************************************"), "F2Pool" },
            { new Address("******************************************"), "HuobiMiningPool" },
            { new Address("******************************************"), "Uleypool" },
            { new Address("******************************************"), "Pooling" },
            { new Address("******************************************"), "MiningPoolHub" },
            { new Address("******************************************"), "MiningPoolHub" },
            { new Address("******************************************"), "Ethermine" },
            { new Address("******************************************"), "BTC.com Pool" }
        };

    }
}
