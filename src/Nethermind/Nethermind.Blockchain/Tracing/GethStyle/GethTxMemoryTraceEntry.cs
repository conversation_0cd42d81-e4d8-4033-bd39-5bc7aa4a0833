// SPDX-FileCopyrightText: 2023 Demerzel Solutions Limited
// SPDX-License-Identifier: LGPL-3.0-only

using System;
using Nethermind.Evm;

namespace Nethermind.Blockchain.Tracing.GethStyle;

public class GethTxMemoryTraceEntry : GethTxTraceEntry
{
    internal override void UpdateMemorySize(ulong size)
    {
        base.UpdateMemorySize(size);

        // <PERSON><PERSON>'s approach to memory trace is to show empty memory spaces on entry for the values that are being set by the operation
        Memory ??= [];

        int missingChunks = (int)((size - (ulong)Memory.Length * EvmPooledMemory.WordSize) / EvmPooledMemory.WordSize);

        if (missingChunks > 0)
        {
            var memory = Memory;
            Array.Resize(ref memory, memory.Length + missingChunks);
            for (int i = Memory.Length; i < memory.Length; i++)
            {
                memory[i] = "0000000000000000000000000000000000000000000000000000000000000000";
            }

            Memory = memory;
        }
    }
}
