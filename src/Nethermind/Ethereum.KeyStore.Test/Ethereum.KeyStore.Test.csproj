<Project Sdk="Microsoft.NET.Sdk">
  
  <Import Project="../tests.props" />
  
  <ItemGroup>
    <ProjectReference Include="..\Ethereum.Test.Base\Ethereum.Test.Base.csproj" />
    <ProjectReference Include="..\Nethermind.Core\Nethermind.Core.csproj" />
    <EmbeddedResource Include="..\..\tests\KeyStoreTests\basic_tests.json" Link="basic_tests.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
</Project>
