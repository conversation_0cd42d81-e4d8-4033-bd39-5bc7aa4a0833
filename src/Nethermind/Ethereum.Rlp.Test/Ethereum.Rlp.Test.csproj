<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="../tests.props" />
  
  <ItemGroup>
    <ProjectReference Include="..\Ethereum.Test.Base\Ethereum.Test.Base.csproj" />
    <EmbeddedResource Include="..\..\tests\RLPTests\rlptest.json">
      <Link>rlptest.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\RLPTests\invalidRLPTest.json">
      <Link>invalidRLPTest.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\RLPTests\RandomRLPTests\example.json">
      <Link>example.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <ProjectReference Include="..\Nethermind.Core.Test\Nethermind.Core.Test.csproj" />
    <ProjectReference Include="..\Nethermind.Serialization.Rlp\Nethermind.Serialization.Rlp.csproj" />
  </ItemGroup>
</Project>
