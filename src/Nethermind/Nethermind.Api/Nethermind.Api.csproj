<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyOriginatorKeyFile>strong.snk</AssemblyOriginatorKeyFile>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Nethermind.Blockchain\Nethermind.Blockchain.csproj" />
    <ProjectReference Include="..\Nethermind.Facade\Nethermind.Facade.csproj" />
    <ProjectReference Include="..\Nethermind.Grpc\Nethermind.Grpc.csproj" />
    <ProjectReference Include="..\Nethermind.JsonRpc\Nethermind.JsonRpc.csproj" />
    <ProjectReference Include="..\Nethermind.Monitoring\Nethermind.Monitoring.csproj" />
    <ProjectReference Include="..\Nethermind.Network\Nethermind.Network.csproj" />
    <ProjectReference Include="..\Nethermind.Sockets\Nethermind.Sockets.csproj" />
    <ProjectReference Include="..\Nethermind.History\Nethermind.History.csproj" />
  </ItemGroup>

</Project>
