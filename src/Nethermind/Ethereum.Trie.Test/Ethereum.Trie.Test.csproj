<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="../tests.props" />
  
  <ItemGroup>
    <ProjectReference Include="..\Ethereum.Test.Base\Ethereum.Test.Base.csproj" />
    <EmbeddedResource Include="..\..\tests\TrieTests\hex_encoded_securetrie_test.json">
      <Link>hex_encoded_securetrie_test.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\TrieTests\trieanyorder.json">
      <Link>trieanyorder.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\TrieTests\trieanyorder_secureTrie.json">
      <Link>trieanyorder_secureTrie.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\TrieTests\trietest.json">
      <Link>trietest.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\TrieTests\trietestnextprev.json">
      <Link>trietestnextprev.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\TrieTests\trietest_secureTrie.json">
      <Link>trietest_secureTrie.json</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
</Project>
