<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="../tests.props" />

  <ItemGroup>
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="FluentAssertions.Json" />
    <PackageReference Include="NSubstitute" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Nethermind.Abi\Nethermind.Abi.csproj" />
    <ProjectReference Include="..\Nethermind.Consensus.AuRa\Nethermind.Consensus.AuRa.csproj" />
    <ProjectReference Include="..\Nethermind.Serialization.Json\Nethermind.Serialization.Json.csproj" />
  </ItemGroup>

</Project>
