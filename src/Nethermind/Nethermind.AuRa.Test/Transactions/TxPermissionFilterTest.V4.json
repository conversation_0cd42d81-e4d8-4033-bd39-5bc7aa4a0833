﻿{
  "name": "TestNodeFilterContract",
  "engine": {
    "authorityRound": {
      "params": {
        "stepDuration": 1,
        "startStep": 2,
        "validators": {
          "contract": "0x0000000000000000000000000000000000000000"
        }
      }
    }
  },
  "params": {
    "accountStartNonce": "0x0",
    "maximumExtraDataSize": "0x20",
    "minGasLimit": "0x1388",
    "networkID" : "0x69",
    "gasLimitBoundDivisor": "0x0400",
    "transactionPermissionContract": "0x0000000000000000000000000000000000000005",
    "transactionPermissionContractTransition": "1",
    "eip1559Transition": "0x1"
  },
  "genesis": {
    "seal": {
      "generic": "0xc180"
    },
    "difficulty": "0x20000",
    "author": "0x0000000000000000000000000000000000000000",
    "timestamp": "0x00",
    "parentHash": "0x0000000000000000000000000000000000000000000000000000000000000000",
    "extraData": "0x",
    "gasLimit": "0x222222"
  },
  "accounts": {
    "0000000000000000000000000000000000000001": { "balance": "1", "builtin": { "name": "ecrecover", "pricing": { "linear": { "base": 3000, "word": 0 } } } },
    "0000000000000000000000000000000000000002": { "balance": "1", "builtin": { "name": "sha256", "pricing": { "linear": { "base": 60, "word": 12 } } } },
    "0000000000000000000000000000000000000003": { "balance": "1", "builtin": { "name": "ripemd160", "pricing": { "linear": { "base": 600, "word": 120 } } } },
    "0000000000000000000000000000000000000004": { "balance": "1", "builtin": { "name": "identity", "pricing": { "linear": { "base": 15, "word": 3 } } } },
    "0xAB5b100cf7C8deFB3c8f3C48474223997A50fB13": {
      "balance": "1",
      "constructor": "608060405234801561001057600080fd5b50610370806100206000396000f300608060405260043610610062576000357c0100000000000000000000000000000000000000000000000000000000900463ffffffff168063469ab1e31461006757806375d0c0dc1461009a578063a0a8e4601461012a578063e4e3b5e514610155575b600080fd5b34801561007357600080fd5b5061007c610251565b60405180826000191660001916815260200191505060405180910390f35b3480156100a657600080fd5b506100af6102c2565b6040518080602001828103825283818151815260200191508051906020019080838360005b838110156100ef5780820151818401526020810190506100d4565b50505050905090810190601f16801561011c5780820380516001836020036101000a031916815260200191505b509250505060405180910390f35b34801561013657600080fd5b5061013f6102ff565b6040518082815260200191505060405180910390f35b34801561016157600080fd5b50610224600480360381019080803573ffffffffffffffffffffffffffffffffffffffff169060200190929190803573ffffffffffffffffffffffffffffffffffffffff16906020019092919080359060200190929190803590602001909291908035906020019092919080359060200190929190803590602001908201803590602001908080601f0160208091040260200160405190810160405280939291908181526020018383808284378201915050505050509192919290505050610308565b604051808363ffffffff1663ffffffff168152602001821515151581526020019250505060405180910390f35b600061025b6102c2565b6040518082805190602001908083835b602083101515610290578051825260208201915060208101905060208303925061026b565b6001836020036101000a0380198251168184511680821785525050505050509050019150506040518091039020905090565b60606040805190810160405280601681526020017f54585f5045524d495353494f4e5f434f4e545241435400000000000000000000815250905090565b60006004905090565b600080600086118061031b575060048351105b156103305763ffffffff600091509150610338565b600080915091505b975097955050505050505600a165627a7a72305820592b45ee74cc856b6c84f99ed8ddd4790d844a9065a07c7bbfc5dfa05cc394c50029"
    }
  }
}
