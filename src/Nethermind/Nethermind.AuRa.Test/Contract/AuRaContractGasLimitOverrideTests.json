﻿{
  "name": "TestNodeFilterContract",
  "engine": {
    "authorityRound": {
      "params": {
        "stepDuration": 1,
        "startStep": 2,
        "validators": {
          "contract": "0x0000000000000000000000000000000000000000"
        },
        "blockGasLimitContractTransitions": {
          "0" : "0xAB5b100cf7C8deFB3c8f3C48474223997A50fB13"
        }
      }
    }
  },
  "params": {
    "accountStartNonce": "0x0",
    "maximumExtraDataSize": "0x20",
    "minGasLimit": "0x1388",
    "networkID" : "0x69",
    "gasLimitBoundDivisor": "0x0400"
  },
  "genesis": {
    "seal": {
      "generic": "0xc180"
    },
    "difficulty": "0x20000",
    "author": "0x0000000000000000000000000000000000000000",
    "timestamp": "0x00",
    "parentHash": "0x0000000000000000000000000000000000000000000000000000000000000000",
    "extraData": "0x",
    "gasLimit": "0x222222"
  },
  "accounts": {
    "0000000000000000000000000000000000000001": { "balance": "1", "builtin": { "name": "ecrecover", "pricing": { "linear": { "base": 3000, "word": 0 } } } },
    "0000000000000000000000000000000000000002": { "balance": "1", "builtin": { "name": "sha256", "pricing": { "linear": { "base": 60, "word": 12 } } } },
    "0000000000000000000000000000000000000003": { "balance": "1", "builtin": { "name": "ripemd160", "pricing": { "linear": { "base": 600, "word": 120 } } } },
    "0000000000000000000000000000000000000004": { "balance": "1", "builtin": { "name": "identity", "pricing": { "linear": { "base": 15, "word": 3 } } } },
    "0xAB5b100cf7C8deFB3c8f3C48474223997A50fB13": {
      "balance": "1",
      "constructor": "6080604052348015600f57600080fd5b50608a8061001e6000396000f3fe6080604052348015600f57600080fd5b506004361060285760003560e01c80637877a79714602d575b600080fd5b60336049565b6040518082815260200191505060405180910390f35b60006305f5e10090509056fea265627a7a7231582021667b0e44480eab1e760db5fd5670ccd02b94e657964ab6e97889519e71fefe64736f6c634300050b0032"
    }
  }
}