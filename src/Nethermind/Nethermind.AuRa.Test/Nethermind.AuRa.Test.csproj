<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="../tests.props" />

  <PropertyGroup>
    <Nullable>annotations</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="NSubstitute" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Nethermind.Blockchain.Test\Nethermind.Blockchain.Test.csproj" />
    <ProjectReference Include="..\Nethermind.Consensus.AuRa\Nethermind.Consensus.AuRa.csproj" />
    <ProjectReference Include="..\Nethermind.Core.Test\Nethermind.Core.Test.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Contract\AuRaContractGasLimitOverrideTests.json" />
    <EmbeddedResource Include="Contract\TxPriorityContractTests.json" />
    <EmbeddedResource Include="Transactions\TxCertifierFilterTests.json" />
    <EmbeddedResource Include="Transactions\TxPermissionFilterTest.V2.json" />
    <EmbeddedResource Include="Transactions\TxPermissionFilterTest.V3.json" />
    <None Remove="Transactions\TxPermissionsFilterTest.V1.json" />
    <EmbeddedResource Include="Transactions\TxPermissionFilterTest.V1.json" />
    <None Remove="Transactions\TxPermissionFilterTest.V4.json" />
    <EmbeddedResource Include="Transactions\TxPermissionFilterTest.V4.json" />
  </ItemGroup>

</Project>
