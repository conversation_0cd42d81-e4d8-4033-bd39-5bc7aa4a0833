<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="../tests.props" />
  
  <ItemGroup>
    <ProjectReference Include="..\Ethereum.Test.Base\Ethereum.Test.Base.csproj" />
    <ProjectReference Include="..\Nethermind.Abi\Nethermind.Abi.csproj" />
    <ProjectReference Include="..\Nethermind.Evm\Nethermind.Evm.csproj" />
    <EmbeddedResource Include="..\..\tests\ABITests\**\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="..\..\tests\GeneralStateTests\VMTests\**\*.*">
      <Link>%(RecursiveDir)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
</Project>
