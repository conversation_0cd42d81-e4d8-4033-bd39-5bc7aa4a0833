# rpc-compat

debug_getRawBlock/get-invalid-number (nethermind)
debug_getRawHeader/get-invalid-number (nethermind)
debug_getRawReceipts/get-invalid-number (nethermind)
debug_getRawTransaction/get-invalid-hash (nethermind)
debug_getRawTransaction/get-tx (nethermind)
eth_call/call-revert-abi-error (nethermind)
eth_call/call-revert-abi-panic (nethermind)
eth_estimateGas/estimate-failed-call (nethermind)
eth_estimateGas/estimate-simple-transfer (nethermind)
eth_estimateGas/estimate-successful-call (nethermind)
eth_feeHistory/fee-history (nethermind)
eth_getBlockByHash/get-block-by-hash (nethermind)
eth_getBlockByNumber/get-block-london-fork (nethermind)
eth_getBlockByNumber/get-finalized (nethermind)
eth_getBlockByNumber/get-genesis (nethermind)
eth_getBlockByNumber/get-latest (nethermind)
eth_getBlockByNumber/get-safe (nethermind)
eth_getBlockReceipts/get-block-receipts-by-hash (nethermind)
eth_getBlockReceipts/get-block-receipts-latest (nethermind)
eth_getBlockReceipts/get-block-receipts-n (nethermind)
eth_getLogs/contract-addr (nethermind)
eth_getLogs/no-topics (nethermind)
eth_getLogs/topic-exact-match (nethermind)
eth_getLogs/topic-wildcard (nethermind)
eth_getProof/get-account-proof-with-storage (nethermind)
eth_getStorageAt/get-storage-invalid-key (nethermind)
eth_getStorageAt/get-storage-invalid-key-too-large (nethermind)
eth_getTransactionReceipt/get-access-list (nethermind)
eth_getTransactionReceipt/get-blob-tx (nethermind)
eth_getTransactionReceipt/get-dynamic-fee (nethermind)
eth_getTransactionReceipt/get-legacy-contract (nethermind)
eth_getTransactionReceipt/get-legacy-input (nethermind)
eth_getTransactionReceipt/get-legacy-receipt (nethermind)
eth_sendRawTransaction/send-blob-tx (nethermind)
eth_simulateV1/ethSimulate-blobs (nethermind)
eth_simulateV1/ethSimulate-empty-with-block-num-set-firstblock (nethermind)
eth_simulateV1/ethSimulate-empty-with-block-num-set-plus1 (nethermind)
eth_simulateV1/ethSimulate-eth-send-should-not-produce-logs-on-revert (nethermind)
eth_simulateV1/ethSimulate-eth-send-should-produce-logs (nethermind)
eth_simulateV1/ethSimulate-eth-send-should-produce-more-logs-on-forward (nethermind)
eth_simulateV1/ethSimulate-eth-send-should-produce-no-logs-on-forward-revert (nethermind)
eth_simulateV1/ethSimulate-fee-recipient-receiving-funds (nethermind)
eth_simulateV1/ethSimulate-gas-fees-and-value-error-38014 (nethermind)
eth_simulateV1/ethSimulate-instrict-gas-38013 (nethermind)
eth_simulateV1/ethSimulate-logs (nethermind)
eth_simulateV1/ethSimulate-make-call-with-future-block (nethermind)
eth_simulateV1/ethSimulate-move-ecrecover-and-call (nethermind)
eth_simulateV1/ethSimulate-move-ecrecover-and-call-old-and-new (nethermind)
eth_simulateV1/ethSimulate-move-ecrecover-twice-and-call (nethermind)
eth_simulateV1/ethSimulate-move-to-address-itself-reference-38022 (nethermind)
eth_simulateV1/ethSimulate-move-two-accounts-to-same-38023 (nethermind)
eth_simulateV1/ethSimulate-move-two-non-precompiles-accounts-to-same (nethermind)
eth_simulateV1/ethSimulate-overflow-nonce (nethermind)
eth_simulateV1/ethSimulate-overflow-nonce-validation (nethermind)
eth_simulateV1/ethSimulate-override-address-twice (nethermind)
eth_simulateV1/ethSimulate-override-address-twice-in-separate-BlockStateCalls (nethermind)
eth_simulateV1/ethSimulate-run-gas-spending (nethermind)
eth_simulateV1/ethSimulate-run-out-of-gas-in-block-38015 (nethermind)
eth_simulateV1/ethSimulate-self-destructive-contract-produces-logs (nethermind)
eth_simulateV1/ethSimulate-send-eth-and-delegate-call (nethermind)
eth_simulateV1/ethSimulate-send-eth-and-delegate-call-to-eoa (nethermind)
eth_simulateV1/ethSimulate-send-eth-and-delegate-call-to-payble-contract (nethermind)
eth_simulateV1/ethSimulate-simple-more-params-validate (nethermind)
eth_simulateV1/ethSimulate-simple-no-funds (nethermind)
eth_simulateV1/ethSimulate-simple-no-funds-with-balance-querying (nethermind)
eth_simulateV1/ethSimulate-simple-send-from-contract (nethermind)
eth_simulateV1/ethSimulate-simple-send-from-contract-no-balance (nethermind)
eth_simulateV1/ethSimulate-transaction-too-high-nonce (nethermind)
eth_simulateV1/ethSimulate-try-to-move-non-precompile (nethermind)
eth_simulateV1/ethSimulate-two-blocks-with-complete-eth-sends (nethermind)
eth_simulateV1/ethSimulate-use-as-many-features-as-possible (nethermind)

# graphql

01_eth_blockNumber (nethermind)
02_eth_call_Block8 (nethermind)
03_eth_call_BlockLatest (nethermind)
04_eth_estimateGas_contractDeploy (nethermind)
05_eth_estimateGas_noParams (nethermind)
06_eth_estimateGas_transfer (nethermind)
07_eth_gasPrice (nethermind)
08_eth_getBalance_0x19 (nethermind)
09_eth_getBalance_invalidAccountBlockNumber (nethermind)
10_eth_getBalance_invalidAccountLatest (nethermind)
11_eth_getBalance_latest (nethermind)
12_eth_getBalance_toobig_bn (nethermind)
13_eth_getBalance_without_addr (nethermind)
14_eth_getBlock_byHash (nethermind)
15_eth_getBlock_byHashInvalid (nethermind)
16_eth_getBlock_byNumber (nethermind)
17_eth_getBlock_byNumberInvalid (nethermind)
18_eth_getBlock_wrongParams (nethermind)
19_eth_getBlockTransactionCount_byHash (nethermind)
20_eth_getBlockTransactionCount_byNumber (nethermind)
21_eth_getCode_noCode (nethermind)
22_eth_getCode (nethermind)
23_eth_getLogs_matchTopic (nethermind)
24_eth_getLogs_range (nethermind)
25_eth_getStorageAt_illegalRangeGreaterThan (nethermind)
26_eth_getStorageAt (nethermind)
27_eth_getTransaction_byBlockHashAndIndex (nethermind)
28_eth_getTransaction_byBlockNumberAndIndex (nethermind)
29_eth_getTransaction_byBlockNumberAndInvalidIndex (nethermind)
30_eth_getTransaction_byHash (nethermind)
31_eth_getTransaction_byHashNull (nethermind)
32_eth_getTransactionCount (nethermind)
33_eth_getTransactionReceipt (nethermind)
34_eth_sendRawTransaction_contractCreation (nethermind)
35_graphql_pending (nethermind)
36_eth_sendRawTransaction_messageCall (nethermind)
37_eth_sendRawTransaction_nonceTooLow (nethermind)
38_eth_sendRawTransaction_transferEther (nethermind)
39_eth_sendRawTransaction_unsignedTransaction (nethermind)
40_eth_syncing (nethermind)
41_graphql_blocks_byFrom (nethermind)
42_graphql_blocks_byRange (nethermind)
43_graphql_blocks_byWrongRange (nethermind)
44_getBlock_byHexNumber (nethermind)
45_eth_getLogs_range_hex (nethermind)
46_transaction_fromByHexBlockNumber (nethermind)
47_block_withdrawals_pre_shanghai (nethermind)
48_block_withdrawals (nethermind)
49_get_type2Transaction (nethermind)
50_eth_getBlock_shanghai (nethermind)
51_eth_getBlock_4844 (nethermind)

#engine

Blob Transaction Ordering, Multiple Accounts (Cancun) (nethermind)
Blob Transaction Ordering, Multiple Clients (Cancun) (nethermind)

#devp2p

BlobViolations (nethermind)
ENRRequest (nethermind)
Findnode/BasicFindnode (nethermind)
Findnode/UnsolicitedNeighbors (nethermind)
FindnodeResults (nethermind)
GetNonexistentBlockHeaders (nethermind)
nethermind
PingMultiIP (nethermind)
SameRequestID (nethermind)
TalkRequest (nethermind)
TestBlobTxWithMismatchedSidecar (nethermind)
TestBlobTxWithoutSidecar (nethermind)
