Source: nethermind
Section: net
Priority: optional
Maintainer: Demerzel Solutions Limited <<EMAIL>>
Build-Depends: debhelper-compat (= 11)
Standards-Version: 4.6.0
Homepage: https://nethermind.io/nethermind-client
Vcs-Browser: https://github.com/NethermindEth/nethermind
Vcs-Git: https://github.com/NethermindEth/nethermind.git
Rules-Requires-Root: no

Package: nethermind
Architecture: amd64 arm64
Depends: ${misc:Depends}, ${shlibs:Depends}, curl, unzip
Description: A robust execution client for Ethereum node operators.
 The Nethermind Ethereum execution client, built on .NET, delivers industry-leading performance in syncing and tip-of-chain processing. With its modular design and plugin system, it offers extensibility and features for new chains. As one of the most adopted execution clients on Ethereum, Nethermind plays a crucial role in enhancing the diversity and resilience of the Ethereum ecosystem.
