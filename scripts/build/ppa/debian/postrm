#!/bin/bash
# SPDX-FileCopyrightText: 2024 Demerzel Solutions Limited
# SPDX-License-Identifier: LGPL-3.0-only

set -e

## summary of how this script can be called:
#        * <postrm> `remove'
#        * <postrm> `purge'
#        * <old-postrm> `upgrade' <new-version>
#        * <new-postrm> `failed-upgrade' <old-version>
#        * <new-postrm> `abort-install'
#        * <new-postrm> `abort-install' <old-version>
#        * <new-postrm> `abort-upgrade' <old-version>
#        * <disappearer's-postrm> `disappear' <overwriter>
#          <overwriter-version>
# for details, see https://www.debian.org/doc/debian-policy/ or
# the debian-policy package

case "$1" in
  purge)
    rm -f /usr/bin/nethermind
    rm -rf /usr/share/nethermind
  ;;

  remove|upgrade|failed-upgrade|abort-install|abort-upgrade|disappear)
    rm -f /usr/bin/nethermind

    if [[ -d /usr/share/nethermind ]]; then
      cd /usr/share/nethermind

      for dir in *; do
        [[ "$dir" == @("nethermind_db"|"keystore") ]] && continue
        rm -rf "$dir"
      done
    fi
  ;;

  *)
    echo "postrm called with unknown argument \`$1'" >&2
    exit 1
  ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0
