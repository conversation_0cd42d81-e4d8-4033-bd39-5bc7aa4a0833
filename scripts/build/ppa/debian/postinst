#!/bin/bash
# SPDX-FileCopyrightText: 2024 Demerzel Solutions Limited
# SPDX-License-Identifier: LGPL-3.0-only

set -e

# summary of how this script can be called:
#        * <postinst> `configure' <most-recently-configured-version>
#        * <old-postinst> `abort-upgrade' <new version>
#        * <conflictor's-postinst> `abort-remove' `in-favour' <package>
#          <new-version>
#        * <postinst> `abort-remove'
#        * <deconfigured's-postinst> `abort-deconfigure' `in-favour'
#          <failed-install-package> <version> `removing'
#          <conflicting-package> <version>
# for details, see https://www.debian.org/doc/debian-policy/ or
# the debian-policy package

case "$1" in
  configure)
    arch=$(dpkg --print-architecture)

    if [[ "$arch" == arm* ]]; then
      url=to_be_replaced
      hash=to_be_replaced
    else
      url=to_be_replaced
      hash=to_be_replaced
    fi

    echo "Downloading $url"
    curl -L -# $url -o nethermind.zip

    echo "$hash  nethermind.zip" | sha256sum -c - || exit 1

    echo "Extracting archive..."
    unzip -oq nethermind.zip -d nethermind

    echo "Starting package install..."
    mkdir -p /usr/share/nethermind
    cp -rf nethermind/* /usr/share/nethermind
    ln -sf /usr/share/nethermind/nethermind /usr/bin/nethermind

    rm -rf nethermind
    rm -f nethermind.zip

    echo "Successfully installed"
  ;;

  abort-upgrade|abort-remove|abort-deconfigure)
  ;;

  *)
    echo "postinst called with unknown argument \`$1'" >&2
    exit 1
  ;;
esac

# dh_installdeb will replace this with shell code automatically
# generated by other debhelper scripts.

#DEBHELPER#

exit 0
